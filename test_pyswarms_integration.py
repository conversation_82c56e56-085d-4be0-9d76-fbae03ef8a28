"""Test script for PySwarms integration with optimagic."""

import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'optimagic', 'src'))

# Test basic import without numpy dependency
print("Testing PySwarms integration...")
print("Checking if module can be imported...")

try:
    from optimagic.optimizers.pyswarms_optimizers import (
        PySwarmsGlobalBestPSO,
        PySwarmsLocalBestPSO,
        PySwarmsGeneralPSO,
        IS_PYSWARMS_INSTALLED,
    )
    print("✓ Successfully imported PySwarms optimizers")
except ImportError as e:
    print(f"✗ Failed to import PySwarms optimizers: {e}")
    sys.exit(1)

print(f"PySwarms installed: {IS_PYSWARMS_INSTALLED}")

if not IS_PYSWARMS_INSTALLED:
    print("PySwarms is not installed. Skipping tests.")
    sys.exit(0)

# Test basic instantiation
try:
    global_best = PySwarmsGlobalBestPSO()
    local_best = PySwarmsLocalBestPSO()
    general = PySwarmsGeneralPSO()
    print("✓ Successfully instantiated all PySwarms optimizers")
except Exception as e:
    print(f"✗ Failed to instantiate optimizers: {e}")
    sys.exit(1)

# Test algorithm info
try:
    print(f"Global Best PSO name: {global_best.name}")
    print(f"Local Best PSO name: {local_best.name}")
    print(f"General PSO name: {general.name}")
    
    print(f"Global Best PSO is global: {global_best.algo_info.is_global}")
    print(f"Global Best PSO supports bounds: {global_best.algo_info.supports_bounds}")
    print(f"Global Best PSO supports parallelism: {global_best.algo_info.supports_parallelism}")
    print("✓ Algorithm info accessible")
except Exception as e:
    print(f"✗ Failed to access algorithm info: {e}")
    sys.exit(1)

# Test with_option method
try:
    modified_global = global_best.with_option(n_particles=100, cognitive_parameter=0.7)
    print(f"Modified n_particles: {modified_global.n_particles}")
    print(f"Modified cognitive_parameter: {modified_global.cognitive_parameter}")
    print("✓ with_option method works")
except Exception as e:
    print(f"✗ with_option method failed: {e}")
    sys.exit(1)

print("\n🎉 All basic tests passed! PySwarms integration is working correctly.")
print("\nNext steps:")
print("1. Run the pre-commit hook to regenerate algorithms.py")
print("2. Test with actual optimization problems")
print("3. Add comprehensive unit tests")
